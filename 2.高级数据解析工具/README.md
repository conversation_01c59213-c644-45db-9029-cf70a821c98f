# 数据解析工具

一个强大的Web工具，用于解析和分析JSON响应数据，特别适用于处理包含Base64编码和GZIP压缩数据的API响应。

## 功能特性

### 🔍 基础解析工具 (index.html)
- ✅ JSON数据解析和格式化
- ✅ Base64数据解码
- ✅ 时间戳格式化
- ✅ 数据类型识别
- ✅ 一键复制功能
- ✅ 响应式设计

### 🔧 高级解析工具 (advanced-parser.html)
- ✅ 所有基础功能
- ✅ GZIP数据解压缩
- ✅ 十六进制数据查看
- ✅ 多标签页界面
- ✅ 压缩比计算
- ✅ 详细的数据分析

## 支持的数据格式

### 输入格式
```json
{
  "code": "0000000",
  "data": "Base64编码的数据...",
  "msg": "成功",
  "ts": 1754374961931
}
```

### 支持的数据类型
- **JSON数据**: 自动格式化和语法高亮
- **纯文本**: 直接显示文本内容
- **GZIP压缩数据**: 自动解压缩并显示内容
- **二进制数据**: 十六进制查看器

## 快速开始

### 方法一：直接打开HTML文件
1. 下载项目文件
2. 直接在浏览器中打开 `index.html` 或 `advanced-parser.html`

### 方法二：使用本地服务器（推荐）
1. 确保已安装 Node.js
2. 在项目目录中运行：
   ```bash
   npm start
   ```
3. 打开浏览器访问：
   - 基础工具: http://localhost:3000/
   - 高级工具: http://localhost:3000/advanced-parser.html

## 使用说明

### 基本使用流程
1. **输入数据**: 将JSON响应数据粘贴到输入框中
2. **解析数据**: 点击"解析数据"按钮
3. **查看结果**: 在结果区域查看解析后的数据
4. **复制内容**: 使用复制按钮快速复制需要的内容

### 高级功能使用
1. **标签页切换**: 在不同视图间切换查看数据
   - **概览**: 显示数据基本信息和统计
   - **解压内容**: 显示GZIP解压后的内容
   - **JSON格式**: 格式化的JSON数据
   - **十六进制**: 二进制数据的十六进制表示
   - **原始数据**: Base64解码后的原始数据

2. **数据分析**: 自动计算压缩比、数据大小等信息

## 技术特性

### 前端技术
- 纯HTML5 + CSS3 + JavaScript
- 响应式设计，支持移动设备
- 使用Pako库进行GZIP解压缩
- 现代浏览器兼容

### 后端服务器
- Node.js HTTP服务器
- 静态文件服务
- CORS支持
- 优雅关闭处理

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 安全说明

- 所有数据处理都在客户端进行
- 不会向服务器发送任何敏感数据
- 支持离线使用

## 常见问题

### Q: 为什么GZIP数据无法解压？
A: 请确保：
1. 数据是有效的Base64编码
2. 解码后的数据确实是GZIP格式
3. 数据没有被截断或损坏

### Q: 如何处理大型数据？
A: 工具支持处理大型数据，但建议：
1. 使用高级解析工具
2. 分段查看十六进制数据
3. 必要时使用浏览器的开发者工具

### Q: 支持其他压缩格式吗？
A: 目前主要支持GZIP格式，其他格式会显示为原始数据或十六进制。

## 更新日志

### v1.0.0
- 初始版本发布
- 基础JSON解析功能
- Base64解码支持
- GZIP解压缩支持
- 十六进制查看器
- 本地服务器支持

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
