<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级数据解析工具</title>
    <script src="https://cdn.jsdelivr.net/npm/pako@2.1.0/dist/pako.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        textarea {
            width: 100%;
            height: 150px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s;
        }

        textarea:focus {
            outline: none;
            border-color: #4facfe;
        }

        .button-group {
            margin: 20px 0;
            text-align: center;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .result-section {
            margin-top: 30px;
        }

        .result-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
        }

        .result-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .info-value {
            color: #333;
            font-size: 1.1em;
        }

        .data-content {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
        }

        .error {
            background: #fee;
            border-left-color: #f56565;
            color: #c53030;
        }

        .success {
            background: #f0fff4;
            border-left-color: #48bb78;
            color: #2f855a;
        }

        .warning {
            background: #fffbeb;
            border-left-color: #f6ad55;
            color: #c05621;
        }

        .timestamp {
            color: #666;
            font-size: 0.9em;
        }

        .copy-btn {
            background: #48bb78;
            font-size: 12px;
            padding: 5px 15px;
            margin-left: 10px;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e1e5e9;
            margin-bottom: 20px;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            border-bottom-color: #4facfe;
            color: #4facfe;
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .hex-view {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
        }

        .hex-line {
            margin-bottom: 2px;
        }

        .hex-offset {
            color: #666;
            margin-right: 15px;
        }

        .hex-bytes {
            color: #333;
            margin-right: 15px;
        }

        .hex-ascii {
            color: #666;
        }

        /* JSON树形结构样式 */
        .json-container {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 13px;
            line-height: 1.4;
            background: #fff;
            padding: 10px;
        }

        .json-tree-line {
            position: relative;
            margin: 0;
            padding-left: 0;
            line-height: 20px;
            min-height: 20px;
        }

        .json-tree-line.level-1 { padding-left: 32px; }
        .json-tree-line.level-2 { padding-left: 64px; }
        .json-tree-line.level-3 { padding-left: 96px; }
        .json-tree-line.level-4 { padding-left: 128px; }
        .json-tree-line.level-5 { padding-left: 160px; }
        .json-tree-line.level-6 { padding-left: 192px; }
        .json-tree-line.level-7 { padding-left: 224px; }
        .json-tree-line.level-8 { padding-left: 256px; }
        .json-tree-line.level-9 { padding-left: 288px; }
        .json-tree-line.level-10 { padding-left: 320px; }
        .json-tree-line.level-11 { padding-left: 352px; }
        .json-tree-line.level-12 { padding-left: 384px; }
        .json-tree-line.level-13 { padding-left: 416px; }
        .json-tree-line.level-14 { padding-left: 448px; }
        .json-tree-line.level-15 { padding-left: 480px; }

        /* 垂直连接线 - 连接到父节点 */
        .json-tree-line.level-1::before { left: 12px; }
        .json-tree-line.level-2::before { left: 44px; }
        .json-tree-line.level-3::before { left: 76px; }
        .json-tree-line.level-4::before { left: 108px; }
        .json-tree-line.level-5::before { left: 140px; }
        .json-tree-line.level-6::before { left: 172px; }
        .json-tree-line.level-7::before { left: 204px; }
        .json-tree-line.level-8::before { left: 236px; }
        .json-tree-line.level-9::before { left: 268px; }
        .json-tree-line.level-10::before { left: 300px; }
        .json-tree-line.level-11::before { left: 332px; }
        .json-tree-line.level-12::before { left: 364px; }
        .json-tree-line.level-13::before { left: 396px; }
        .json-tree-line.level-14::before { left: 428px; }
        .json-tree-line.level-15::before { left: 460px; }

        .json-tree-line::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 50%;
            width: 1px;
            background: #d0d0d0;
        }

        /* 水平连接线 - 连接到当前节点 */
        .json-tree-line.level-1::after { left: 12px; }
        .json-tree-line.level-2::after { left: 44px; }
        .json-tree-line.level-3::after { left: 76px; }
        .json-tree-line.level-4::after { left: 108px; }
        .json-tree-line.level-5::after { left: 140px; }
        .json-tree-line.level-6::after { left: 172px; }
        .json-tree-line.level-7::after { left: 204px; }
        .json-tree-line.level-8::after { left: 236px; }
        .json-tree-line.level-9::after { left: 268px; }
        .json-tree-line.level-10::after { left: 300px; }
        .json-tree-line.level-11::after { left: 332px; }
        .json-tree-line.level-12::after { left: 364px; }
        .json-tree-line.level-13::after { left: 396px; }
        .json-tree-line.level-14::after { left: 428px; }
        .json-tree-line.level-15::after { left: 460px; }

        .json-tree-line::after {
            content: '';
            position: absolute;
            top: 10px;
            width: 14px;
            height: 1px;
            background: #d0d0d0;
        }

        /* 最后一个子节点的垂直线只到中点 */
        .json-tree-line.last-child::before {
            bottom: 50%;
        }

        /* 根节点不显示连接线 */
        .json-tree-line.root::before,
        .json-tree-line.root::after {
            display: none;
        }

        /* 为父节点添加延续的垂直线 */
        .json-children {
            position: relative;
        }

        .json-children::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: #d0d0d0;
        }

        .json-tree-line.level-1 + .json-children::before { left: 12px; }
        .json-tree-line.level-2 + .json-children::before { left: 44px; }
        .json-tree-line.level-3 + .json-children::before { left: 76px; }
        .json-tree-line.level-4 + .json-children::before { left: 108px; }
        .json-tree-line.level-5 + .json-children::before { left: 140px; }
        .json-tree-line.level-6 + .json-children::before { left: 172px; }
        .json-tree-line.level-7 + .json-children::before { left: 204px; }
        .json-tree-line.level-8 + .json-children::before { left: 236px; }
        .json-tree-line.level-9 + .json-children::before { left: 268px; }
        .json-tree-line.level-10 + .json-children::before { left: 300px; }
        .json-tree-line.level-11 + .json-children::before { left: 332px; }
        .json-tree-line.level-12 + .json-children::before { left: 364px; }
        .json-tree-line.level-13 + .json-children::before { left: 396px; }
        .json-tree-line.level-14 + .json-children::before { left: 428px; }
        .json-tree-line.level-15 + .json-children::before { left: 460px; }

        /* 通用规则：超过15层的处理 */
        .json-tree-line[class*="level-"] {
            position: relative;
        }

        /* 动态计算深层级的缩进 */
        .json-tree-line.deep-level {
            padding-left: calc(var(--level) * 32px);
        }

        .json-tree-line.deep-level::before {
            left: calc(var(--level) * 32px - 20px);
        }

        .json-tree-line.deep-level::after {
            left: calc(var(--level) * 32px - 20px);
        }

        .json-toggle {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 4px;
            cursor: pointer;
            text-align: center;
            font-size: 12px;
            color: #666;
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 2px;
            user-select: none;
            vertical-align: middle;
            line-height: 14px;
            position: relative;
            z-index: 1;
        }

        .json-toggle:hover {
            background: #e8e8e8;
            border-color: #bbb;
        }

        .json-toggle.collapsed::before {
            content: '⊞';
            color: #666;
        }

        .json-toggle.expanded::before {
            content: '⊟';
            color: #666;
        }

        .json-toggle.empty {
            visibility: hidden;
        }

        .json-type-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 4px;
            text-align: center;
            font-size: 12px;
            line-height: 16px;
            border-radius: 2px;
            color: white;
            font-weight: bold;
        }

        .json-type-object {
            background: #4a90e2;
        }

        .json-type-array {
            background: #f5a623;
        }

        .json-type-string {
            background: #7ed321;
        }

        .json-type-number {
            background: #bd10e0;
        }

        .json-type-boolean {
            background: #50e3c2;
        }

        .json-type-null {
            background: #9013fe;
        }

        .json-key {
            color: #333;
            font-weight: bold;
            margin-right: 4px;
        }

        .json-value {
            color: #666;
        }

        .json-string-value {
            color: #d73a49;
        }

        .json-number-value {
            color: #005cc5;
        }

        .json-boolean-value {
            color: #e36209;
        }

        .json-null-value {
            color: #6f42c1;
        }

        .json-summary {
            color: #999;
            font-style: italic;
            margin-left: 8px;
        }

        .json-children {
            margin-left: 0;
        }

        .json-controls {
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .json-controls button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 12px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 8px;
            transition: background 0.2s;
        }

        .json-controls button:hover {
            background: #5a6268;
        }

        .json-actions {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-top: 1px solid #e1e5e9;
        }

        .json-actions button {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 15px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 8px;
            transition: background 0.2s;
        }

        .json-actions button:hover {
            background: #218838;
        }

        .json-actions button.secondary {
            background: #6c757d;
        }

        .json-actions button.secondary:hover {
            background: #5a6268;
        }

        .json-paste-area {
            margin-top: 10px;
        }

        .json-paste-input {
            width: 100%;
            height: 80px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            resize: vertical;
        }

        .json-paste-input:focus {
            outline: none;
            border-color: #4facfe;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 高级数据解析工具</h1>
            <p>支持GZIP解压缩、十六进制查看、多格式数据解析</p>
        </div>
        
        <div class="content">
            <div class="input-section">
                <h2>📝 输入数据</h2>
                <textarea id="inputData" placeholder="请粘贴您的JSON数据..."></textarea>
            </div>
            
            <div class="button-group">
                <button onclick="parseData()">🚀 解析数据</button>
                <button onclick="clearAll()">🗑️ 清空</button>
                <button onclick="loadSample()">📋 加载示例</button>
            </div>
            
            <div id="results" class="result-section"></div>
        </div>
    </div>

    <script>
        // 加载示例数据
        function loadSample() {
            const sampleData = {
                "code": "0000000",
                "data": "H4sIAAAAAAAAAMVcbW8j13X+Kwt+KFpY2r3vL/tNdovUgTdexJvEieEPWokrKRVJlaQ2CAwDBpwGQdKiQYAYCZIgRRDUDWo4bV0HbpOif8Zaa/9FnzPDmblvIw4p2l1opeFw5s655/U55547b42OD5eHo/tvvDWaHJ0dj+6PRnuj5fTbRzh69sH7z/7zPz795L3rj7+Hs0ezxaOXZvPpeD66/+TwfDHGqcUU946u//DRZz9+d3XJw8vp2eK0u2S2ePVyOT87OV22506OFqP7bG90erF49el4vjybjEHBm3ujyQku",
                "msg": "成功",
                "ts": 1754374961931
            };
            document.getElementById('inputData').value = JSON.stringify(sampleData, null, 2);
        }

        // 清空所有内容
        function clearAll() {
            document.getElementById('inputData').value = '';
            document.getElementById('results').innerHTML = '';
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('已复制到剪贴板！');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('已复制到剪贴板！');
            });
        }

        // 格式化时间戳
        function formatTimestamp(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 生成十六进制视图
        function generateHexView(uint8Array) {
            let html = '<div class="hex-view">';
            for (let i = 0; i < uint8Array.length; i += 16) {
                const offset = i.toString(16).padStart(8, '0').toUpperCase();
                let hexBytes = '';
                let asciiChars = '';
                
                for (let j = 0; j < 16; j++) {
                    if (i + j < uint8Array.length) {
                        const byte = uint8Array[i + j];
                        hexBytes += byte.toString(16).padStart(2, '0').toUpperCase() + ' ';
                        asciiChars += (byte >= 32 && byte <= 126) ? String.fromCharCode(byte) : '.';
                    } else {
                        hexBytes += '   ';
                        asciiChars += ' ';
                    }
                }
                
                html += `<div class="hex-line">
                    <span class="hex-offset">${offset}</span>
                    <span class="hex-bytes">${hexBytes}</span>
                    <span class="hex-ascii">${asciiChars}</span>
                </div>`;
            }
            html += '</div>';
            return html;
        }

        // 切换标签页
        function switchTab(tabName, element) {
            // 隐藏所有标签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            element.classList.add('active');
        }

        // JSON树形结构生成
        function createCollapsibleJson(obj, level = 0, key = null, isLast = true, isRoot = false) {
            const getTypeInfo = (value) => {
                if (value === null) return { type: 'null', icon: '空', text: 'null' };
                if (Array.isArray(value)) return { type: 'array', icon: '组', text: `[${value.length}]` };
                if (typeof value === 'object') return { type: 'object', icon: '象', text: `{${Object.keys(value).length}}` };
                if (typeof value === 'string') return { type: 'string', icon: '文', text: `"${escapeHtml(value)}"` };
                if (typeof value === 'number') return { type: 'number', icon: '数', text: value.toString() };
                if (typeof value === 'boolean') return { type: 'boolean', icon: '布', text: value.toString() };
                return { type: 'unknown', icon: '?', text: String(value) };
            };

            const typeInfo = getTypeInfo(obj);
            const toggleId = `toggle_${Math.random().toString(36).substr(2, 9)}`;
            const contentId = `content_${Math.random().toString(36).substr(2, 9)}`;

            let html = '';
            let levelClass = '';
            let styleAttr = '';

            if (level > 0) {
                if (level <= 15) {
                    levelClass = `level-${level}`;
                } else {
                    levelClass = 'deep-level';
                    styleAttr = `style="--level: ${level};"`;
                }
            }

            const lineClass = `json-tree-line ${levelClass} ${isLast ? 'last-child' : ''} ${isRoot ? 'root' : ''}`;

            // 简单值直接显示
            if (!Array.isArray(obj) && typeof obj !== 'object') {
                html += `<div class="${lineClass}" ${styleAttr}>`;
                html += `<span class="json-type-icon json-type-${typeInfo.type}">${typeInfo.icon}</span>`;
                if (key !== null) {
                    html += `<span class="json-key">${escapeHtml(key)}:</span> `;
                }
                html += `<span class="json-value json-${typeInfo.type}-value">${typeInfo.text}</span>`;
                html += `</div>`;
                return html;
            }

            // null值处理
            if (obj === null) {
                html += `<div class="${lineClass}" ${styleAttr}>`;
                html += `<span class="json-type-icon json-type-null">空</span>`;
                if (key !== null) {
                    html += `<span class="json-key">${escapeHtml(key)}:</span> `;
                }
                html += `<span class="json-value json-null-value">null</span>`;
                html += `</div>`;
                return html;
            }

            // 复杂对象处理
            const isEmpty = (Array.isArray(obj) && obj.length === 0) ||
                           (typeof obj === 'object' && Object.keys(obj).length === 0);

            html += `<div class="${lineClass}" ${styleAttr}>`;

            if (!isEmpty) {
                html += `<span class="json-toggle expanded" id="${toggleId}" onclick="toggleJsonNode('${toggleId}', '${contentId}')"></span>`;
            } else {
                html += `<span class="json-toggle empty"></span>`;
            }

            html += `<span class="json-type-icon json-type-${typeInfo.type}">${typeInfo.icon}</span>`;

            if (key !== null) {
                html += `<span class="json-key">${escapeHtml(key)}:</span> `;
            }

            html += `<span class="json-summary" id="${contentId}_summary" style="display: none;">${typeInfo.text}</span>`;
            html += `</div>`;

            // 子元素
            if (!isEmpty) {
                const childrenClass = level > 0 ? `json-children level-${level}-children` : 'json-children';
                html += `<div class="${childrenClass}" id="${contentId}">`;

                if (Array.isArray(obj)) {
                    obj.forEach((item, index) => {
                        const isLastChild = index === obj.length - 1;
                        html += createCollapsibleJson(item, level + 1, `[${index}]`, isLastChild, false);
                    });
                } else {
                    const keys = Object.keys(obj);
                    keys.forEach((objKey, index) => {
                        const isLastChild = index === keys.length - 1;
                        html += createCollapsibleJson(obj[objKey], level + 1, objKey, isLastChild, false);
                    });
                }

                html += `</div>`;
            }

            return html;
        }

        // 切换JSON节点展开/折叠
        function toggleJsonNode(toggleId, contentId) {
            const toggle = document.getElementById(toggleId);
            const content = document.getElementById(contentId);
            const summary = document.getElementById(contentId + '_summary');

            if (toggle.classList.contains('expanded')) {
                toggle.classList.remove('expanded');
                toggle.classList.add('collapsed');
                content.style.display = 'none';
                if (summary) summary.style.display = 'inline';
            } else {
                toggle.classList.remove('collapsed');
                toggle.classList.add('expanded');
                content.style.display = 'block';
                if (summary) summary.style.display = 'none';
            }
        }

        // 展开所有JSON节点
        function expandAllJson() {
            const toggles = document.querySelectorAll('.json-toggle');
            toggles.forEach(toggle => {
                if (toggle.classList.contains('collapsed')) {
                    toggle.click();
                }
            });
        }

        // 折叠所有JSON节点
        function collapseAllJson() {
            const toggles = document.querySelectorAll('.json-toggle');
            toggles.forEach(toggle => {
                if (toggle.classList.contains('expanded')) {
                    toggle.click();
                }
            });
        }

        // 折叠到指定层级
        function collapseToLevel(targetLevel) {
            const toggles = document.querySelectorAll('.json-toggle');
            toggles.forEach(toggle => {
                const level = (toggle.closest('.json-line').querySelector('.json-indent').textContent.length / 2);
                if (level >= targetLevel && toggle.classList.contains('expanded')) {
                    toggle.click();
                }
            });
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 复制JSON数据
        function copyJsonData(data, format = 'formatted') {
            let textToCopy;

            if (format === 'formatted') {
                textToCopy = JSON.stringify(data, null, 2);
            } else if (format === 'minified') {
                textToCopy = JSON.stringify(data);
            } else {
                textToCopy = String(data);
            }

            copyToClipboard(textToCopy);
        }

        // 显示粘贴区域
        function showPasteArea(containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const existingPasteArea = container.querySelector('.json-paste-area');
            if (existingPasteArea) {
                existingPasteArea.remove();
                return;
            }

            const pasteArea = document.createElement('div');
            pasteArea.className = 'json-paste-area';
            pasteArea.innerHTML = `
                <textarea class="json-paste-input" placeholder="粘贴JSON数据到这里..."></textarea>
                <div style="margin-top: 8px;">
                    <button onclick="applyPastedJson('${containerId}')" style="background: #28a745; color: white; border: none; padding: 5px 12px; border-radius: 3px; font-size: 12px; cursor: pointer; margin-right: 8px;">应用</button>
                    <button onclick="hidePasteArea('${containerId}')" style="background: #6c757d; color: white; border: none; padding: 5px 12px; border-radius: 3px; font-size: 12px; cursor: pointer;">取消</button>
                </div>
            `;

            container.appendChild(pasteArea);
            pasteArea.querySelector('.json-paste-input').focus();
        }

        // 隐藏粘贴区域
        function hidePasteArea(containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const pasteArea = container.querySelector('.json-paste-area');
            if (pasteArea) {
                pasteArea.remove();
            }
        }

        // 应用粘贴的JSON数据
        function applyPastedJson(containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const pasteInput = container.querySelector('.json-paste-input');
            if (!pasteInput) return;

            const jsonText = pasteInput.value.trim();
            if (!jsonText) {
                alert('请输入JSON数据');
                return;
            }

            try {
                const jsonData = JSON.parse(jsonText);

                // 找到对应的数据容器并更新
                const dataContent = container.querySelector('.data-content .json-container');
                if (dataContent) {
                    dataContent.innerHTML = createCollapsibleJson(jsonData, 0, null, true, true);
                }

                hidePasteArea(containerId);
                alert('JSON数据已更新！');
            } catch (error) {
                alert('JSON格式错误: ' + error.message);
            }
        }

        // 导出JSON到文件
        function exportJsonToFile(data, filename = 'data.json') {
            const jsonString = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 全局存储当前JSON数据
        let currentJsonData = {};

        // 尝试解码Base64数据
        function decodeBase64Data(data) {
            try {
                // 尝试直接解码Base64
                const decoded = atob(data);
                
                // 转换为Uint8Array
                const uint8Array = new Uint8Array(decoded.length);
                for (let i = 0; i < decoded.length; i++) {
                    uint8Array[i] = decoded.charCodeAt(i);
                }
                
                let result = {
                    raw: decoded,
                    uint8Array: uint8Array,
                    hexView: generateHexView(uint8Array)
                };
                
                // 检查是否是gzip压缩（以1f 8b开头）
                if (uint8Array[0] === 0x1f && uint8Array[1] === 0x8b) {
                    try {
                        // 使用pako解压gzip数据
                        const decompressed = pako.inflate(uint8Array, { to: 'string' });
                        result.type = 'gzip';
                        result.decompressed = decompressed;
                        
                        // 尝试解析解压后的数据为JSON
                        try {
                            result.json = JSON.parse(decompressed);
                        } catch {
                            // 不是JSON，保持为文本
                        }
                        
                        return result;
                    } catch (error) {
                        result.type = 'gzip_error';
                        result.error = '解压失败: ' + error.message;
                        return result;
                    }
                }
                
                // 尝试解析为JSON
                try {
                    const jsonData = JSON.parse(decoded);
                    result.type = 'json';
                    result.json = jsonData;
                    return result;
                } catch {
                    // 如果不是JSON，返回原始文本
                    result.type = 'text';
                    result.text = decoded;
                    return result;
                }
            } catch (error) {
                return {
                    type: 'error',
                    error: '无法解码Base64数据: ' + error.message
                };
            }
        }

        // 主解析函数
        function parseData() {
            const input = document.getElementById('inputData').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!input) {
                resultsDiv.innerHTML = '<div class="result-card error"><h3>❌ 错误</h3><p>请输入要解析的数据</p></div>';
                return;
            }
            
            try {
                // 解析JSON
                const jsonData = JSON.parse(input);
                
                let html = '<div class="result-card success"><h3>✅ 解析成功</h3></div>';
                
                // 显示基本信息
                html += '<div class="result-card"><h3>📊 基本信息</h3><div class="info-grid">';
                
                if (jsonData.code !== undefined) {
                    html += `<div class="info-item">
                        <div class="info-label">状态码</div>
                        <div class="info-value">${jsonData.code}</div>
                    </div>`;
                }
                
                if (jsonData.msg !== undefined) {
                    html += `<div class="info-item">
                        <div class="info-label">消息</div>
                        <div class="info-value">${jsonData.msg}</div>
                    </div>`;
                }
                
                if (jsonData.ts !== undefined) {
                    html += `<div class="info-item">
                        <div class="info-label">时间戳</div>
                        <div class="info-value">${jsonData.ts}<br><span class="timestamp">${formatTimestamp(jsonData.ts)}</span></div>
                    </div>`;
                }
                
                html += '</div></div>';
                
                // 解析data字段
                if (jsonData.data) {
                    const decodedData = decodeBase64Data(jsonData.data);
                    
                    html += '<div class="result-card"><h3>🔓 数据解析结果</h3>';
                    
                    if (decodedData.type === 'error') {
                        html += `<p style="color: #c53030;">${decodedData.error}</p>`;
                    } else {
                        // 创建标签页
                        html += '<div class="tabs">';
                        html += '<div class="tab active" onclick="switchTab(\'overview\', this)">概览</div>';
                        
                        if (decodedData.type === 'gzip' && decodedData.decompressed) {
                            html += '<div class="tab" onclick="switchTab(\'decompressed\', this)">解压内容</div>';
                        }
                        if (decodedData.json) {
                            html += '<div class="tab" onclick="switchTab(\'json\', this)">JSON格式</div>';
                        }
                        html += '<div class="tab" onclick="switchTab(\'hex\', this)">十六进制</div>';
                        html += '<div class="tab" onclick="switchTab(\'raw\', this)">原始数据</div>';
                        html += '</div>';
                        
                        // 概览标签页
                        html += '<div id="overview" class="tab-content active">';
                        html += `<p><strong>数据类型:</strong> ${decodedData.type.toUpperCase()}</p>`;
                        html += `<p><strong>原始Base64长度:</strong> ${jsonData.data.length} 字符</p>`;
                        html += `<p><strong>解码后长度:</strong> ${decodedData.uint8Array.length} 字节</p>`;
                        
                        if (decodedData.type === 'gzip') {
                            if (decodedData.decompressed) {
                                html += `<p><strong>解压后长度:</strong> ${decodedData.decompressed.length} 字符</p>`;
                                html += `<p><strong>压缩比:</strong> ${((1 - decodedData.uint8Array.length / decodedData.decompressed.length) * 100).toFixed(1)}%</p>`;
                            } else if (decodedData.error) {
                                html += `<p style="color: #c53030;"><strong>错误:</strong> ${decodedData.error}</p>`;
                            }
                        }
                        html += '</div>';
                        
                        // 解压内容标签页
                        if (decodedData.type === 'gzip' && decodedData.decompressed) {
                            html += '<div id="decompressed" class="tab-content">';

                            // 尝试解析解压后的内容为JSON
                            let decompressedJson = null;
                            try {
                                decompressedJson = JSON.parse(decodedData.decompressed);
                            } catch (e) {
                                // 不是JSON格式，保持原样
                            }

                            if (decompressedJson) {
                                const decompressedId = 'decompressed_' + Math.random().toString(36).substr(2, 9);
                                const decompressedKey = 'decompressedData_' + decompressedId;
                                window[decompressedKey] = decompressedJson;

                                html += '<div class="json-controls">';
                                html += '<button onclick="expandAllJson()">展开全部</button>';
                                html += '<button onclick="collapseAllJson()">折叠全部</button>';
                                html += '<button onclick="collapseToLevel(1)">折叠到1级</button>';
                                html += '<button onclick="collapseToLevel(2)">折叠到2级</button>';
                                html += '<button onclick="collapseToLevel(3)">折叠到3级</button>';
                                html += '</div>';
                                html += `<div id="${decompressedId}" class="data-content"><div class="json-container">${createCollapsibleJson(decompressedJson, 0, null, true, true)}</div></div>`;
                                html += '<div class="json-actions">';
                                html += `<button onclick="copyJsonData(window['${decompressedKey}'], 'formatted')">📋 复制格式化</button>`;
                                html += `<button onclick="copyJsonData(window['${decompressedKey}'], 'minified')">📋 复制压缩</button>`;
                                html += `<button onclick="exportJsonToFile(window['${decompressedKey}'], 'decompressed-data.json')">💾 导出文件</button>`;
                                html += `<button class="secondary" onclick="showPasteArea('${decompressedId}')">📥 粘贴替换</button>`;
                                html += '</div>';
                            } else {
                                html += '<div class="data-content">' + decodedData.decompressed + '</div>';
                                html += '<div class="json-actions">';
                                html += `<button onclick="copyToClipboard(\`${decodedData.decompressed.replace(/`/g, '\\`')}\`)">📋 复制内容</button>`;
                                html += '</div>';
                            }
                            html += '</div>';
                        }
                        
                        // JSON格式标签页
                        if (decodedData.json) {
                            // 存储当前JSON数据到全局变量
                            const jsonId = 'json_' + Math.random().toString(36).substr(2, 9);
                            const dataKey = 'jsonData_' + jsonId;
                            window[dataKey] = decodedData.json;

                            html += '<div id="json" class="tab-content">';
                            html += '<div class="json-controls">';
                            html += '<button onclick="expandAllJson()">展开全部</button>';
                            html += '<button onclick="collapseAllJson()">折叠全部</button>';
                            html += '<button onclick="collapseToLevel(1)">折叠到1级</button>';
                            html += '<button onclick="collapseToLevel(2)">折叠到2级</button>';
                            html += '<button onclick="collapseToLevel(3)">折叠到3级</button>';
                            html += '</div>';
                            html += `<div id="${jsonId}" class="data-content"><div class="json-container">${createCollapsibleJson(decodedData.json, 0, null, true, true)}</div></div>`;
                            html += '<div class="json-actions">';
                            html += `<button onclick="copyJsonData(window['${dataKey}'], 'formatted')">📋 复制格式化</button>`;
                            html += `<button onclick="copyJsonData(window['${dataKey}'], 'minified')">📋 复制压缩</button>`;
                            html += `<button onclick="exportJsonToFile(window['${dataKey}'], 'decoded-data.json')">💾 导出文件</button>`;
                            html += `<button class="secondary" onclick="showPasteArea('${jsonId}')">📥 粘贴替换</button>`;
                            html += '</div>';
                            html += '</div>';
                        }
                        
                        // 十六进制标签页
                        html += '<div id="hex" class="tab-content">';
                        html += '<div class="data-content">' + decodedData.hexView + '</div>';
                        html += '</div>';
                        
                        // 原始数据标签页
                        html += '<div id="raw" class="tab-content">';
                        html += '<div class="data-content">' + decodedData.raw + '</div>';
                        html += `<button class="copy-btn" onclick="copyToClipboard(\`${decodedData.raw.replace(/`/g, '\\`')}\`)">复制原始数据</button>`;
                        html += '</div>';
                    }
                    
                    html += '</div>';
                }
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="result-card error">
                    <h3>❌ 解析失败</h3>
                    <p>JSON格式错误: ${error.message}</p>
                    <p>请检查输入的数据格式是否正确。</p>
                </div>`;
            }
        }
    </script>
</body>
</html>
